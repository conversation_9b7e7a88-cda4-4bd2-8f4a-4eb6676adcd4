import 'package:dartz/dartz.dart';
import '../../domain/entities/flashcard.dart';
import '../../domain/repositories/flashcard_repository.dart';
import '../../../../core/error/failures.dart';

/// Mock implementation of FlashcardRepository for development and testing
class MockFlashcardRepository implements FlashcardRepository {
  // In-memory storage for mock data
  final Map<String, Flashcard> _flashcards = {};
  final Map<String, List<FlashcardReview>> _reviews = {};

  @override
  Future<Either<Failure, void>> saveFlashcard(Flashcard flashcard) async {
    await Future.delayed(const Duration(milliseconds: 100));
    _flashcards[flashcard.id] = flashcard;
    return const Right(null);
  }

  @override
  Future<Either<Failure, void>> saveFlashcards(
    List<Flashcard> flashcards,
  ) async {
    await Future.delayed(const Duration(milliseconds: 200));
    for (final flashcard in flashcards) {
      _flashcards[flashcard.id] = flashcard;
    }
    return const Right(null);
  }

  @override
  Future<Either<Failure, Flashcard?>> getFlashcard(String id) async {
    await Future.delayed(const Duration(milliseconds: 50));
    return Right(_flashcards[id]);
  }

  @override
  Future<Either<Failure, List<Flashcard>>> getFlashcardsByTopic({
    required String topic,
    String? subject,
    DifficultyLevel? difficulty,
  }) async {
    await Future.delayed(const Duration(milliseconds: 100));

    final filtered = _flashcards.values.where((card) {
      bool matches = card.topic.toLowerCase().contains(topic.toLowerCase());

      if (subject != null) {
        matches =
            matches &&
            card.subject.toLowerCase().contains(subject.toLowerCase());
      }

      if (difficulty != null) {
        matches = matches && card.difficulty == difficulty;
      }

      return matches;
    }).toList();

    return Right(filtered);
  }

  @override
  Future<Either<Failure, List<Flashcard>>> getFlashcardsBySubject(
    String subject,
  ) async {
    await Future.delayed(const Duration(milliseconds: 100));

    final filtered = _flashcards.values
        .where(
          (card) => card.subject.toLowerCase().contains(subject.toLowerCase()),
        )
        .toList();

    return Right(filtered);
  }

  @override
  Future<Either<Failure, List<Flashcard>>> getDueFlashcards({
    String? userId,
    String? subject,
    String? topic,
  }) async {
    await Future.delayed(const Duration(milliseconds: 100));

    final now = DateTime.now();
    final due = _flashcards.values.where((card) {
      bool isDue =
          card.nextReview.isBefore(now) ||
          card.nextReview.isAtSameMomentAs(now);

      if (subject != null) {
        isDue =
            isDue && card.subject.toLowerCase().contains(subject.toLowerCase());
      }

      if (topic != null) {
        isDue = isDue && card.topic.toLowerCase().contains(topic.toLowerCase());
      }

      return isDue;
    }).toList();

    return Right(due);
  }

  @override
  Future<Either<Failure, Flashcard>> updateFlashcardAfterReview({
    required String flashcardId,
    required FlashcardResponse response,
  }) async {
    await Future.delayed(const Duration(milliseconds: 100));

    final flashcard = _flashcards[flashcardId];
    if (flashcard == null) {
      return const Left(DataFailure('Flashcard not found'));
    }

    // Simple spaced repetition algorithm
    int newInterval;
    double newEaseFactor = flashcard.easeFactor;

    switch (response) {
      case FlashcardResponse.hard:
        newInterval = 1;
        newEaseFactor = (flashcard.easeFactor - 0.2).clamp(1.3, 2.5);
        break;
      case FlashcardResponse.good:
        if (flashcard.reviewCount == 0) {
          newInterval = 1;
        } else if (flashcard.reviewCount == 1) {
          newInterval = 6;
        } else {
          newInterval = (flashcard.interval * flashcard.easeFactor).round();
        }
        break;
      case FlashcardResponse.easy:
        newInterval = (flashcard.interval * flashcard.easeFactor * 1.3).round();
        newEaseFactor = flashcard.easeFactor + 0.1;
        break;
    }

    final now = DateTime.now();
    final updatedFlashcard = flashcard.copyWith(
      lastReviewed: now,
      nextReview: now.add(Duration(days: newInterval)),
      reviewCount: flashcard.reviewCount + 1,
      easeFactor: newEaseFactor,
      interval: newInterval,
    );

    _flashcards[flashcardId] = updatedFlashcard;

    // Record the review
    final review = FlashcardReview(
      id: 'review_${now.millisecondsSinceEpoch}',
      flashcardId: flashcardId,
      reviewDate: now,
      response: response,
      intervalBefore: flashcard.interval,
      intervalAfter: newInterval,
      easeFactorBefore: flashcard.easeFactor,
      easeFactorAfter: newEaseFactor,
      reviewTime: const Duration(
        seconds: 30,
      ), // TODO: Review time tracking will be implemented with user interaction analytics
    );

    _reviews.putIfAbsent(flashcardId, () => []);
    _reviews[flashcardId]!.add(review);

    return Right(updatedFlashcard);
  }

  @override
  Future<Either<Failure, void>> deleteFlashcard(String id) async {
    await Future.delayed(const Duration(milliseconds: 50));
    _flashcards.remove(id);
    _reviews.remove(id);
    return const Right(null);
  }

  @override
  Future<Either<Failure, void>> deleteFlashcards(List<String> ids) async {
    await Future.delayed(const Duration(milliseconds: 100));
    for (final id in ids) {
      _flashcards.remove(id);
      _reviews.remove(id);
    }
    return const Right(null);
  }

  @override
  Future<Either<Failure, List<Flashcard>>> searchFlashcards({
    required String query,
    String? subject,
    String? topic,
    DifficultyLevel? difficulty,
  }) async {
    await Future.delayed(const Duration(milliseconds: 150));

    final lowerQuery = query.toLowerCase();
    final results = _flashcards.values.where((card) {
      bool matches =
          card.front.toLowerCase().contains(lowerQuery) ||
          card.back.toLowerCase().contains(lowerQuery) ||
          card.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));

      if (subject != null) {
        matches =
            matches &&
            card.subject.toLowerCase().contains(subject.toLowerCase());
      }

      if (topic != null) {
        matches =
            matches && card.topic.toLowerCase().contains(topic.toLowerCase());
      }

      if (difficulty != null) {
        matches = matches && card.difficulty == difficulty;
      }

      return matches;
    }).toList();

    return Right(results);
  }

  @override
  Future<Either<Failure, FlashcardStats>> getFlashcardStats({
    String? userId,
    String? subject,
    String? topic,
  }) async {
    await Future.delayed(const Duration(milliseconds: 100));

    var cards = _flashcards.values;

    if (subject != null) {
      cards = cards.where(
        (card) => card.subject.toLowerCase().contains(subject.toLowerCase()),
      );
    }

    if (topic != null) {
      cards = cards.where(
        (card) => card.topic.toLowerCase().contains(topic.toLowerCase()),
      );
    }

    final cardList = cards.toList();
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    final stats = FlashcardStats(
      totalFlashcards: cardList.length,
      dueFlashcards: cardList.where((card) => card.isDue).length,
      newFlashcards: cardList.where((card) => card.isNew).length,
      reviewedToday: cardList.where((card) {
        final lastReview = DateTime(
          card.lastReviewed.year,
          card.lastReviewed.month,
          card.lastReviewed.day,
        );
        return lastReview.isAtSameMomentAs(today);
      }).length,
      masteredFlashcards: cardList
          .where((card) => card.easeFactor > 2.5 && card.reviewCount > 5)
          .length,
      averageEaseFactor: cardList.isEmpty
          ? 2.5
          : cardList.map((card) => card.easeFactor).reduce((a, b) => a + b) /
                cardList.length,
      difficultyDistribution: _getDifficultyDistribution(cardList),
      subjectDistribution: _getSubjectDistribution(cardList),
    );

    return Right(stats);
  }

  @override
  Future<Either<Failure, String>> exportFlashcards({
    required List<String> flashcardIds,
    required ExportFormat format,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final cards = flashcardIds
        .map((id) => _flashcards[id])
        .where((card) => card != null)
        .cast<Flashcard>()
        .toList();

    switch (format) {
      case ExportFormat.json:
        return Right('{"flashcards": [${cards.length} cards exported]}');
      case ExportFormat.csv:
        return Right(
          'front,back,subject,topic\n${cards.length} cards exported',
        );
      case ExportFormat.anki:
        return Right('Anki package with ${cards.length} cards');
      case ExportFormat.quizlet:
        return Right('Quizlet format with ${cards.length} cards');
    }
  }

  @override
  Future<Either<Failure, List<Flashcard>>> importFlashcards({
    required String data,
    required ExportFormat format,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    // TODO: Real import functionality for CSV, JSON, and other formats will be implemented
    // This mock provides sample imported flashcards for development and testing
    final now = DateTime.now();
    final imported = List.generate(3, (index) {
      return Flashcard(
        id: 'imported_${now.millisecondsSinceEpoch}_$index',
        front: 'Imported Question ${index + 1}',
        back: 'Imported Answer ${index + 1}',
        subject: 'Imported Subject',
        topic: 'Imported Topic',
        tags: ['imported'],
        difficulty: DifficultyLevel.medium,
        createdAt: now,
        lastReviewed: now,
        nextReview: now.add(const Duration(days: 1)),
        reviewCount: 0,
        easeFactor: 2.5,
        interval: 1,
      );
    });

    // Save imported cards
    for (final card in imported) {
      _flashcards[card.id] = card;
    }

    return Right(imported);
  }

  @override
  Future<Either<Failure, Flashcard?>> getNextReviewFlashcard({
    String? userId,
    String? subject,
    String? topic,
  }) async {
    await Future.delayed(const Duration(milliseconds: 50));

    final dueResult = await getDueFlashcards(
      userId: userId,
      subject: subject,
      topic: topic,
    );

    return dueResult.fold((failure) => Left(failure), (dueCards) {
      if (dueCards.isEmpty) return const Right(null);

      // Sort by priority (overdue cards first, then by ease factor)
      dueCards.sort((a, b) {
        final aDaysOverdue = DateTime.now().difference(a.nextReview).inDays;
        final bDaysOverdue = DateTime.now().difference(b.nextReview).inDays;

        if (aDaysOverdue != bDaysOverdue) {
          return bDaysOverdue.compareTo(aDaysOverdue); // More overdue first
        }

        return a.easeFactor.compareTo(b.easeFactor); // Lower ease factor first
      });

      return Right(dueCards.first);
    });
  }

  @override
  Future<Either<Failure, void>> toggleFlashcardFavorite(
    String flashcardId,
  ) async {
    await Future.delayed(const Duration(milliseconds: 50));

    final flashcard = _flashcards[flashcardId];
    if (flashcard == null) {
      return const Left(DataFailure('Flashcard not found'));
    }

    // Mock favorite toggle by adding/removing 'favorite' tag
    final tags = List<String>.from(flashcard.tags);
    if (tags.contains('favorite')) {
      tags.remove('favorite');
    } else {
      tags.add('favorite');
    }

    _flashcards[flashcardId] = flashcard.copyWith(tags: tags);
    return const Right(null);
  }

  @override
  Future<Either<Failure, List<Flashcard>>> getFavoriteFlashcards({
    String? userId,
    String? subject,
  }) async {
    await Future.delayed(const Duration(milliseconds: 100));

    var favorites = _flashcards.values.where(
      (card) => card.tags.contains('favorite'),
    );

    if (subject != null) {
      favorites = favorites.where(
        (card) => card.subject.toLowerCase().contains(subject.toLowerCase()),
      );
    }

    return Right(favorites.toList());
  }

  @override
  Future<Either<Failure, List<FlashcardReview>>> getFlashcardReviewHistory({
    required String flashcardId,
    int? limit,
  }) async {
    await Future.delayed(const Duration(milliseconds: 50));

    final reviews = _reviews[flashcardId] ?? [];
    final sortedReviews = List<FlashcardReview>.from(reviews)
      ..sort((a, b) => b.reviewDate.compareTo(a.reviewDate));

    if (limit != null && limit > 0) {
      return Right(sortedReviews.take(limit).toList());
    }

    return Right(sortedReviews);
  }

  @override
  Future<Either<Failure, void>> resetFlashcardProgress(
    String flashcardId,
  ) async {
    await Future.delayed(const Duration(milliseconds: 50));

    final flashcard = _flashcards[flashcardId];
    if (flashcard == null) {
      return const Left(DataFailure('Flashcard not found'));
    }

    final now = DateTime.now();
    _flashcards[flashcardId] = flashcard.copyWith(
      lastReviewed: now,
      nextReview: now.add(const Duration(days: 1)),
      reviewCount: 0,
      easeFactor: 2.5,
      interval: 1,
    );

    // Clear review history
    _reviews.remove(flashcardId);

    return const Right(null);
  }

  /// Helper method to get difficulty distribution
  Map<DifficultyLevel, int> _getDifficultyDistribution(List<Flashcard> cards) {
    final distribution = <DifficultyLevel, int>{};

    for (final level in DifficultyLevel.values) {
      distribution[level] = cards
          .where((card) => card.difficulty == level)
          .length;
    }

    return distribution;
  }

  /// Helper method to get subject distribution
  Map<String, int> _getSubjectDistribution(List<Flashcard> cards) {
    final distribution = <String, int>{};

    for (final card in cards) {
      distribution[card.subject] = (distribution[card.subject] ?? 0) + 1;
    }

    return distribution;
  }
}
