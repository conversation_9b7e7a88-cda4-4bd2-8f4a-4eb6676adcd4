import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../domain/entities/learning_progress.dart';
import '../../domain/repositories/ai_tutor_repository.dart';
import '../../data/repositories/mock_ai_tutor_repository.dart';
import '../../domain/services/ai_content_service.dart';

/// Enhanced progress chart widget with interactive charts and analytics
/// Displays learning progress, quiz scores, and study streaks
/// Integrates with real user progress data from the repository
class ProgressChartWidget extends StatefulWidget {
  final List<ProgressData>? progressData;
  final String chartType;
  final String title;
  final String? subject;
  final String? topic;

  const ProgressChartWidget({
    super.key,
    this.progressData,
    this.chartType = 'line',
    this.title = 'Learning Progress',
    this.subject,
    this.topic,
  });

  @override
  State<ProgressChartWidget> createState() => _ProgressChartWidgetState();
}

class _ProgressChartWidgetState extends State<ProgressChartWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedPeriod = 'Week';
  String _selectedMetric = 'Score';
  late AITutorRepository _repository;
  List<ProgressData>? _cachedProgressData;
  bool _isLoading = false;
  bool _showErrorState = false;
  bool _showEmptyState = false;
  bool _showAuthRequiredState = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _repository = MockAITutorRepository(aiContentService: AIContentService());
    _loadProgressData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Loads real progress data from repository
  Future<void> _loadProgressData() async {
    if (widget.progressData != null) {
      _cachedProgressData = widget.progressData;
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // Load progress data from repository
        final progressResult = await _repository.getLearningProgress(
          userId: user.uid,
          subject: widget.subject ?? 'General',
          topic: widget.topic,
        );

        progressResult.fold(
          (failure) {
            // Show error state with user-friendly message and retry option
            _showErrorState = true;
            _errorMessage = _getErrorMessage(failure);
            _cachedProgressData = [];
          },
          (learningProgress) {
            if (learningProgress != null) {
              _cachedProgressData = _convertLearningProgressToProgressData(
                learningProgress,
              );
              _showErrorState = false;
            } else {
              // Show empty state with onboarding message
              _showEmptyState = true;
              _cachedProgressData = [];
            }
          },
        );
      } else {
        // Handle unauthenticated state with proper message
        _showAuthRequiredState = true;
        _cachedProgressData = [];
      }
    } catch (e) {
      // Implement proper error handling with user feedback
      _showErrorState = true;
      _errorMessage = 'Unable to load progress data. Please try again.';
      _cachedProgressData = [];
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Gets user-friendly error message from failure
  String _getErrorMessage(dynamic failure) {
    if (failure.toString().contains('network')) {
      return 'Network error. Please check your connection and try again.';
    } else if (failure.toString().contains('auth')) {
      return 'Authentication error. Please sign in again.';
    } else {
      return 'Unable to load progress data. Please try again.';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        elevation: 4,
        margin: EdgeInsets.all(16),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Loading progress data...'),
              ],
            ),
          ),
        ),
      );
    }

    // Handle error state
    if (_showErrorState) {
      return _buildErrorState();
    }

    // Handle empty state
    if (_showEmptyState) {
      return _buildEmptyState();
    }

    // Handle authentication required state
    if (_showAuthRequiredState) {
      return _buildAuthRequiredState();
    }

    final progressData = _cachedProgressData ?? [];

    // If no data available, show empty state
    if (progressData.isEmpty) {
      return _buildEmptyState();
    }

    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.title,
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                Row(
                  children: [
                    // Period selector
                    DropdownButton<String>(
                      value: _selectedPeriod,
                      items: ['Day', 'Week', 'Month', 'Year']
                          .map(
                            (period) => DropdownMenuItem(
                              value: period,
                              child: Text(period),
                            ),
                          )
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedPeriod = value!;
                          _loadProgressData(); // Reload data when period changes
                        });
                      },
                    ),
                    const SizedBox(width: 8),
                    // Metric selector
                    DropdownButton<String>(
                      value: _selectedMetric,
                      items: ['Score', 'Time', 'Streak']
                          .map(
                            (metric) => DropdownMenuItem(
                              value: metric,
                              child: Text(metric),
                            ),
                          )
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedMetric = value!;
                        });
                      },
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Tab bar for different chart types
            TabBar(
              controller: _tabController,
              labelColor: Theme.of(context).primaryColor,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Theme.of(context).primaryColor,
              tabs: const [
                Tab(icon: Icon(Icons.show_chart), text: 'Line'),
                Tab(icon: Icon(Icons.bar_chart), text: 'Bar'),
                Tab(icon: Icon(Icons.pie_chart), text: 'Pie'),
              ],
            ),
            const SizedBox(height: 16),

            // Chart content
            SizedBox(
              height: 300,
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildLineChart(progressData),
                  _buildBarChart(progressData),
                  _buildPieChart(progressData),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Summary statistics
            _buildSummaryStats(progressData),
          ],
        ),
      ),
    );
  }

  /// Builds error state widget with retry option
  Widget _buildErrorState() {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
              const SizedBox(height: 16),
              Text(
                'Oops! Something went wrong',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                _errorMessage,
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[600]),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _showErrorState = false;
                    _errorMessage = '';
                  });
                  _loadProgressData();
                },
                icon: const Icon(Icons.refresh),
                label: const Text('Try Again'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds empty state widget with onboarding message
  Widget _buildEmptyState() {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.analytics_outlined, size: 64, color: Colors.blue[300]),
              const SizedBox(height: 16),
              Text(
                'Start Your Learning Journey!',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'Complete some quizzes or flashcard sessions to see your progress here.',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[600]),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  // Navigate to learning activities
                  Navigator.of(context).pop();
                },
                icon: const Icon(Icons.school),
                label: const Text('Start Learning'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds authentication required state widget
  Widget _buildAuthRequiredState() {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.login, size: 64, color: Colors.orange[300]),
              const SizedBox(height: 16),
              Text(
                'Sign In Required',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'Please sign in to view your learning progress and statistics.',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[600]),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  // Navigate to sign in
                  Navigator.of(context).pushNamed('/login');
                },
                icon: const Icon(Icons.login),
                label: const Text('Sign In'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLineChart(List<ProgressData> data) {
    return LineChart(
      LineChartData(
        gridData: FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: const TextStyle(fontSize: 12),
                );
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < data.length) {
                  return Text(
                    '${data[index].date.day}/${data[index].date.month}',
                    style: const TextStyle(fontSize: 10),
                  );
                }
                return const Text('');
              },
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(show: true),
        lineBarsData: [
          LineChartBarData(
            spots: data
                .asMap()
                .entries
                .map(
                  (entry) => FlSpot(
                    entry.key.toDouble(),
                    entry.value.score.toDouble(),
                  ),
                )
                .toList(),
            isCurved: true,
            color: Theme.of(context).primaryColor,
            barWidth: 3,
            dotData: const FlDotData(show: true),
            belowBarData: BarAreaData(
              show: true,
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBarChart(List<ProgressData> data) {
    return BarChart(
      BarChartData(
        gridData: FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: const TextStyle(fontSize: 12),
                );
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < data.length) {
                  return Text(
                    '${data[index].date.day}/${data[index].date.month}',
                    style: const TextStyle(fontSize: 10),
                  );
                }
                return const Text('');
              },
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(show: true),
        barGroups: data
            .asMap()
            .entries
            .map(
              (entry) => BarChartGroupData(
                x: entry.key,
                barRods: [
                  BarChartRodData(
                    toY: entry.value.score.toDouble(),
                    color: Theme.of(context).primaryColor,
                    width: 16,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(4),
                      topRight: Radius.circular(4),
                    ),
                  ),
                ],
              ),
            )
            .toList(),
      ),
    );
  }

  Widget _buildPieChart(List<ProgressData> data) {
    // Calculate subject-wise progress breakdown from actual data
    final subjectData = _calculateSubjectBreakdown(data);

    if (subjectData.isEmpty) {
      return const Center(
        child: Text(
          'No data available for pie chart',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return PieChart(
      PieChartData(
        sections: subjectData.entries
            .map(
              (entry) => PieChartSectionData(
                value: entry.value,
                title: '${entry.value.toInt()}%',
                color: _getColorForSubject(entry.key),
                radius: 100,
                titleStyle: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            )
            .toList(),
        centerSpaceRadius: 40,
        sectionsSpace: 2,
      ),
    );
  }

  /// Calculates subject-wise progress breakdown from progress data
  Map<String, double> _calculateSubjectBreakdown(List<ProgressData> data) {
    if (data.isEmpty) return {};

    // Group data by subject and calculate averages
    final subjectGroups = <String, List<ProgressData>>{};
    for (final item in data) {
      subjectGroups.putIfAbsent(item.subject, () => []).add(item);
    }

    final subjectAverages = <String, double>{};
    for (final entry in subjectGroups.entries) {
      final avgScore =
          entry.value.map((d) => d.score.toDouble()).reduce((a, b) => a + b) /
          entry.value.length;
      subjectAverages[entry.key] = avgScore;
    }

    // If only one subject, show time distribution instead
    if (subjectAverages.length == 1) {
      return _calculateTimeDistribution(data);
    }

    return subjectAverages;
  }

  /// Calculates time distribution when only one subject is available
  Map<String, double> _calculateTimeDistribution(List<ProgressData> data) {
    final totalMinutes = data
        .map((d) => d.timeSpent.inMinutes.toDouble())
        .reduce((a, b) => a + b);

    if (totalMinutes == 0) {
      return {'No Activity': 100.0};
    }

    // Group by day of week for time distribution
    final dayGroups = <String, double>{};
    for (final item in data) {
      final dayName = _getDayName(item.date.weekday);
      dayGroups[dayName] = (dayGroups[dayName] ?? 0) + item.timeSpent.inMinutes;
    }

    // Convert to percentages
    return dayGroups.map(
      (day, minutes) => MapEntry(day, (minutes / totalMinutes) * 100),
    );
  }

  /// Gets day name from weekday number
  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'Monday';
      case 2:
        return 'Tuesday';
      case 3:
        return 'Wednesday';
      case 4:
        return 'Thursday';
      case 5:
        return 'Friday';
      case 6:
        return 'Saturday';
      case 7:
        return 'Sunday';
      default:
        return 'Unknown';
    }
  }

  Widget _buildSummaryStats(List<ProgressData> data) {
    final avgScore = data.isEmpty
        ? 0.0
        : data.map((d) => d.score).reduce((a, b) => a + b) / data.length;
    final totalSessions = data.length;
    final bestScore = data.isEmpty
        ? 0
        : data.map((d) => d.score).reduce((a, b) => a > b ? a : b);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildStatItem(
          'Avg Score',
          '${avgScore.toStringAsFixed(1)}%',
          Icons.trending_up,
        ),
        _buildStatItem('Sessions', totalSessions.toString(), Icons.school),
        _buildStatItem('Best Score', '$bestScore%', Icons.star),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).primaryColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
      ],
    );
  }

  Color _getColorForSubject(String subject) {
    // Enhanced color mapping for various subjects and day names
    switch (subject.toLowerCase()) {
      case 'mathematics':
      case 'math':
        return Colors.blue;
      case 'science':
      case 'physics':
      case 'chemistry':
      case 'biology':
        return Colors.green;
      case 'language':
      case 'english':
      case 'literature':
        return Colors.orange;
      case 'history':
      case 'social studies':
        return Colors.purple;
      case 'monday':
        return Colors.red;
      case 'tuesday':
        return Colors.pink;
      case 'wednesday':
        return Colors.indigo;
      case 'thursday':
        return Colors.teal;
      case 'friday':
        return Colors.amber;
      case 'saturday':
        return Colors.cyan;
      case 'sunday':
        return Colors.deepOrange;
      case 'no activity':
        return Colors.grey.shade300;
      default:
        // Generate a consistent color based on subject name hash
        final hash = subject.hashCode;
        final colors = [
          Colors.blue,
          Colors.green,
          Colors.orange,
          Colors.purple,
          Colors.red,
          Colors.pink,
          Colors.indigo,
          Colors.teal,
          Colors.amber,
          Colors.cyan,
        ];
        return colors[hash.abs() % colors.length];
    }
  }

  /// Converts LearningProgress to ProgressData for chart display
  List<ProgressData> _convertLearningProgressToProgressData(
    LearningProgress learningProgress,
  ) {
    final weeklyActivity = learningProgress.stats.weeklyActivity;
    final now = DateTime.now();

    // Generate progress data for the last 7 days
    return List.generate(7, (index) {
      final date = now.subtract(Duration(days: 6 - index));
      final dayKey =
          '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

      // Get actual data from weekly activity or use calculated values
      final timeSpent = weeklyActivity[dayKey] ?? 0;
      final score = _calculateDayScore(learningProgress, date);

      return ProgressData(
        date: date,
        score: score,
        timeSpent: Duration(minutes: timeSpent),
        subject: learningProgress.subject,
      );
    });
  }

  /// Calculates a score for a specific day based on learning progress
  int _calculateDayScore(LearningProgress progress, DateTime date) {
    // Base score from overall progress
    final baseScore = (progress.overallProgress * 100).round();

    // Add variation based on recent activity
    final daysSinceLastStudy = DateTime.now()
        .difference(progress.stats.lastStudyDate)
        .inDays;
    final activityBonus = daysSinceLastStudy <= 1 ? 10 : 0;

    // Add streak bonus
    final streakBonus = progress.stats.streakDays > 5 ? 5 : 0;

    return (baseScore + activityBonus + streakBonus).clamp(0, 100);
  }
}

/// Data model for progress tracking
class ProgressData {
  final DateTime date;
  final int score;
  final Duration timeSpent;
  final String subject;

  ProgressData({
    required this.date,
    required this.score,
    required this.timeSpent,
    required this.subject,
  });
}
