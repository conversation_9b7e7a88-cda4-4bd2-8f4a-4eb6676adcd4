import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:developer' as developer;
import 'package:diogeneschatbot/features/ai_tutor/presentation/bloc/ai_tutor_bloc.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/services/service_locator.dart';
import 'package:diogeneschatbot/models/usage.dart';
import '../widgets/progress_chart_widget.dart';
import '../widgets/learning_plan_widget.dart';
import '../widgets/learning_plan_setup_widget.dart';
import '../widgets/flashcards_tab_widget.dart';
import '../widgets/quiz_tab_widget.dart';
import '../../domain/entities/learning_progress.dart';
import '../../domain/entities/flashcard.dart'; // For DifficultyLevel
import '../../domain/repositories/ai_tutor_repository.dart';
import '../../data/repositories/mock_ai_tutor_repository.dart';
import '../../domain/services/ai_content_service.dart';

/// Main AI Tutor page with comprehensive learning features
/// Merged from AITutorPage and AITutorDashboardPage for consistency
/// Features include: learning plans, flashcards, quizzes, and progress tracking
/// Future enhancements: personalized recommendations, adaptive difficulty,
/// social learning features, calendar integration, and gamification elements
class AITutorPage extends StatefulWidget {
  final String? title;
  final Usage? usage;

  const AITutorPage({super.key, this.title, this.usage});

  @override
  State<AITutorPage> createState() => _AITutorPageState();
}

class _AITutorPageState extends State<AITutorPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _currentIndex = 0;

  // User preferences state
  bool _studyRemindersEnabled = true;
  String _selectedDifficulty = 'Medium';

  late AITutorRepository _repository;

  // Progress data state
  LearningProgress? _userProgress;
  bool _isLoadingProgress = false;
  String _totalStudyTime = '0h 0m';
  String _averageScore = '0%';
  String _studyStreak = '0 days';
  String _conceptsMastered = '0';

  // Learning plan form state variables (merged from dashboard page)
  String? _selectedSubject;
  String? _selectedLevel;
  final List<String> _learningGoals = [];
  final TextEditingController _goalController = TextEditingController();

  // Available options for learning plan setup
  final List<String> _subjects = [
    'Mathematics',
    'Science',
    'History',
    'Literature',
    'Computer Science',
    'Languages',
    'Art',
    'Music',
  ];

  final List<String> _levels = [
    'Beginner',
    'Intermediate',
    'Advanced',
    'Expert',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this); // Updated to 5 tabs
    _tabController.addListener(() {
      setState(() {
        _currentIndex = _tabController.index;
      });
    });
    _repository = MockAITutorRepository(aiContentService: AIContentService());
    _loadUserProgress();
    _loadUserPreferences();
  }

  /// Loads user progress data from repository
  Future<void> _loadUserProgress() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    setState(() {
      _isLoadingProgress = true;
    });

    try {
      final progressResult = await _repository.getLearningProgress(
        userId: user.uid,
        subject: 'General', // Can be made dynamic based on user selection
      );

      progressResult.fold(
        (failure) {
          // Use default values on failure
          _updateProgressStats(null);
        },
        (progress) {
          _updateProgressStats(progress);
        },
      );
    } catch (e) {
      _updateProgressStats(null);
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingProgress = false;
        });
      }
    }
  }

  /// Updates progress statistics from learning progress data
  void _updateProgressStats(LearningProgress? progress) {
    if (progress == null) {
      // Show default values for new users with helpful hints
      _totalStudyTime = '0h 0m';
      _averageScore = 'N/A';
      _studyStreak = '0 days';
      _conceptsMastered = '0';
      return;
    }

    _userProgress = progress;

    // Calculate total study time
    final totalMinutes = progress.stats.totalStudyTime;
    final hours = totalMinutes ~/ 60;
    final minutes = totalMinutes % 60;
    _totalStudyTime = '${hours}h ${minutes}m';

    // Format average score
    _averageScore = '${progress.stats.averageQuizScore.toStringAsFixed(0)}%';

    // Format study streak
    _studyStreak = '${progress.stats.streakDays} days';

    // Count mastered concepts
    _conceptsMastered = progress.masteredConcepts.length.toString();
  }

  /// Loads user preferences from local storage or Firebase
  Future<void> _loadUserPreferences() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // TODO: Implement actual preference loading from Firebase/SharedPreferences
      // For now, use default values
      setState(() {
        _studyRemindersEnabled = true;
        _selectedDifficulty = 'Medium';
      });
    } catch (e) {
      developer.log('Failed to load user preferences: $e');
    }
  }

  /// Saves user preferences to local storage or Firebase
  Future<void> _saveUserPreferences() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // TODO: Implement actual preference saving to Firebase/SharedPreferences
      developer.log(
        'Saving preferences: reminders=$_studyRemindersEnabled, difficulty=$_selectedDifficulty',
      );
    } catch (e) {
      developer.log('Failed to save user preferences: $e');
    }
  }

  /// Parses difficulty level string to DifficultyLevel enum
  DifficultyLevel _parseDifficultyLevel(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return DifficultyLevel.easy;
      case 'medium':
        return DifficultyLevel.medium;
      case 'hard':
        return DifficultyLevel.hard;
      case 'expert':
        return DifficultyLevel.expert;
      default:
        return DifficultyLevel.medium;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _goalController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<AITutorBloc>(),
      child: Scaffold(
        appBar: EnhancedAppBar(
          title: widget.title ?? 'AI Tutor',
          showBackButton:
              widget.title !=
              null, // Show back button when title is provided (dashboard mode)
          actions: [
            IconButton(
              icon: const Icon(Icons.analytics),
              onPressed: () => _navigateToAnalytics(),
              tooltip: 'Learning Analytics',
            ),
            IconButton(
              icon: const Icon(Icons.settings),
              onPressed: () => _showSettingsDialog(context),
              tooltip: 'Tutor Settings',
            ),
            IconButton(
              icon: const Icon(Icons.help_outline),
              onPressed: () => _showHelpDialog(context),
              tooltip: 'Help & Tips',
            ),
          ],
        ),
        body: Column(
          children: [
            // Tab bar with enhanced styling
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TabBar(
                controller: _tabController,
                labelColor: AppTheme.primaryGreen,
                unselectedLabelColor: Colors.grey,
                indicatorColor: AppTheme.primaryGreen,
                indicatorWeight: 3,
                labelStyle: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
                tabs: const [
                  Tab(icon: Icon(Icons.dashboard), text: 'Dashboard'),
                  Tab(icon: Icon(Icons.map), text: 'Learning Plan'),
                  Tab(icon: Icon(Icons.style), text: 'Flashcards'),
                  Tab(icon: Icon(Icons.quiz), text: 'Quiz'),
                  Tab(icon: Icon(Icons.trending_up), text: 'Progress'),
                ],
              ),
            ),
            // Tab content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildDashboardTab(),
                  _buildLearningPlanTab(),
                  _buildFlashcardsTab(),
                  _buildQuizTab(),
                  _buildProgressTab(),
                ],
              ),
            ),
          ],
        ),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  Widget _buildDashboardTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome section
          EnhancedCard(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.school,
                          color: AppTheme.primaryGreen,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Welcome to AI Tutor!',
                              style: Theme.of(context).textTheme.headlineSmall
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.primaryGreen,
                                  ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Your personalized learning journey starts here',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(color: Colors.grey[600]),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  // Quick stats
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'Study Streak',
                          _studyStreak,
                          Icons.local_fire_department,
                          Colors.orange,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          'Cards Reviewed',
                          _userProgress?.stats.flashcardsReviewed.toString() ??
                              '0',
                          Icons.style,
                          Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          'Quizzes Taken',
                          _userProgress?.stats.quizzesCompleted.toString() ??
                              '0',
                          Icons.quiz,
                          Colors.green,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Quick actions
          Text(
            'Quick Actions',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildActionCard(
                  'Create Learning Plan',
                  'Start a new subject',
                  Icons.add_circle,
                  AppTheme.primaryGreen,
                  () => _tabController.animateTo(1),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionCard(
                  'Practice Flashcards',
                  'Review your cards',
                  Icons.style,
                  Colors.blue,
                  () => _tabController.animateTo(2),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildActionCard(
                  'Take Quiz',
                  'Test your knowledge',
                  Icons.quiz,
                  Colors.purple,
                  () =>
                      _tabController.animateTo(3), // Fixed: Quiz tab is index 3
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionCard(
                  'View Progress',
                  'Track your learning',
                  Icons.analytics,
                  Colors.orange,
                  () => _tabController.animateTo(
                    4,
                  ), // Fixed: Progress tab is index 4
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLearningPlanTab() {
    return BlocBuilder<AITutorBloc, AITutorState>(
      builder: (context, state) {
        if (state is AITutorInitial) {
          return LearningPlanSetupWidget(
            selectedSubject: _selectedSubject,
            selectedLevel: _selectedLevel,
            learningGoals: _learningGoals,
            subjects: _subjects,
            levels: _levels,
            onSubjectChanged: (value) {
              setState(() {
                _selectedSubject = value;
              });
            },
            onLevelChanged: (value) {
              setState(() {
                _selectedLevel = value;
              });
            },
            onGoalAdded: (goal) {
              setState(() {
                _learningGoals.add(goal);
              });
            },
            onGoalRemoved: (goal) {
              setState(() {
                _learningGoals.remove(goal);
              });
            },
            onGeneratePlan: _generateLearningPlan,
          );
        } else if (state is AITutorLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Generating your personalized learning plan...'),
              ],
            ),
          );
        } else if (state is LearningPlanGenerated) {
          return LearningPlanWidget(learningPlan: state.learningPlan);
        } else if (state is AITutorError) {
          return _buildErrorWidget(state.message);
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildFlashcardsTab() {
    return FlashcardsTabWidget(
      onCreateFlashcards: _showCreateFlashcardsDialog,
      stats: _getMockStats(),
    );
  }

  Widget _buildQuizTab() {
    return QuizTabWidget(
      onCreateQuiz: _showCreateQuizDialog,
      stats: _getMockStats(),
    );
  }

  Widget _buildProgressTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Progress overview header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Progress Analytics',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              Row(
                children: [
                  // Filter button
                  IconButton(
                    icon: const Icon(Icons.filter_list),
                    onPressed: () => _showProgressFilters(),
                    tooltip: 'Filter progress data',
                  ),
                  // Export button
                  IconButton(
                    icon: const Icon(Icons.download),
                    onPressed: () => _exportProgressData(),
                    tooltip: 'Export progress data',
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Quick stats cards
          Row(
            children: [
              Expanded(
                child: _buildProgressStatCard(
                  'Total Study Time',
                  _isLoadingProgress ? 'Loading...' : _totalStudyTime,
                  Icons.access_time,
                  Colors.blue,
                  'This week',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildProgressStatCard(
                  'Average Score',
                  _isLoadingProgress ? 'Loading...' : _averageScore,
                  Icons.trending_up,
                  Colors.green,
                  '+5% from last week',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildProgressStatCard(
                  'Study Streak',
                  _isLoadingProgress ? 'Loading...' : _studyStreak,
                  Icons.local_fire_department,
                  Colors.orange,
                  'Personal best!',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildProgressStatCard(
                  'Concepts Mastered',
                  _isLoadingProgress ? 'Loading...' : _conceptsMastered,
                  Icons.psychology,
                  Colors.purple,
                  '3 this week',
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Progress chart section
          Text(
            'Learning Progress Chart',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          // Enhanced ProgressChartWidget with real data integration
          ProgressChartWidget(
            title: 'Learning Progress Chart',
            subject: _userProgress?.subject ?? 'General',
            topic: _userProgress?.topic,
          ),
          const SizedBox(height: 24),

          // Recent activity section
          Text(
            'Recent Activity',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          _buildRecentActivityList(),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.2)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    switch (_currentIndex) {
      case 1: // Learning Plan tab
        return FloatingActionButton.extended(
          onPressed: () => _showCreatePlanDialog(),
          icon: const Icon(Icons.add),
          label: const Text('New Plan'),
          backgroundColor: AppTheme.primaryGreen,
        );
      case 2: // Flashcards tab
        return FloatingActionButton.extended(
          onPressed: () => _showCreateFlashcardsDialog(),
          icon: const Icon(Icons.style),
          label: const Text('Create Cards'),
          backgroundColor: AppTheme.primaryGreen,
        );
      case 3: // Quiz tab
        return FloatingActionButton.extended(
          onPressed: () => _showCreateQuizDialog(),
          icon: const Icon(Icons.quiz),
          label: const Text('Create Quiz'),
          backgroundColor: AppTheme.primaryGreen,
        );
      default:
        return null;
    }
  }

  void _showSettingsDialog(BuildContext context) {
    // Enhanced settings dialog with user-friendly options
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.settings, color: AppTheme.primaryGreen),
            SizedBox(width: 8),
            Text('AI Tutor Settings'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.notifications),
                title: const Text('Study Reminders'),
                subtitle: const Text('Get notified about study sessions'),
                trailing: Switch(
                  value: _studyRemindersEnabled,
                  onChanged: (value) {
                    setState(() {
                      _studyRemindersEnabled = value;
                    });
                    _saveUserPreferences();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          value ? 'Reminders enabled' : 'Reminders disabled',
                        ),
                      ),
                    );
                  },
                ),
              ),
              ListTile(
                leading: const Icon(Icons.speed),
                title: const Text('Difficulty Level'),
                subtitle: const Text('Adjust learning difficulty'),
                trailing: DropdownButton<String>(
                  value: _selectedDifficulty,
                  items: ['Easy', 'Medium', 'Hard', 'Expert']
                      .map(
                        (level) =>
                            DropdownMenuItem(value: level, child: Text(level)),
                      )
                      .toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedDifficulty = value;
                      });
                      _saveUserPreferences();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Difficulty set to: $value')),
                      );
                    }
                  },
                ),
              ),
              ListTile(
                leading: const Icon(Icons.psychology),
                title: const Text('AI Model'),
                subtitle: const Text('Choose AI model for tutoring'),
                trailing: const Text('GPT-4'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog(BuildContext context) {
    // Enhanced help dialog with tips and tutorials
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.help_outline, color: AppTheme.primaryGreen),
            SizedBox(width: 8),
            Text('Help & Tips'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '🎯 Getting Started:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text('• Create a learning plan for your subject'),
              const Text('• Generate flashcards for key concepts'),
              const Text('• Take adaptive quizzes to test knowledge'),
              const Text('• Track your progress over time'),
              const SizedBox(height: 16),
              const Text(
                '💡 Pro Tips:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text('• Review flashcards daily for best retention'),
              const Text('• Use different explanation styles'),
              const Text('• Focus on identified knowledge gaps'),
              const Text('• Set study reminders in settings'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _showCreatePlanDialog() {
    // Enhanced create learning plan dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.add_circle, color: AppTheme.primaryGreen),
            SizedBox(width: 8),
            Text('Create Learning Plan'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('📚 Quick Start Options:'),
              const SizedBox(height: 16),
              ListTile(
                leading: const Icon(Icons.calculate),
                title: const Text('Mathematics'),
                subtitle: const Text('Algebra, Calculus, Geometry'),
                onTap: () {
                  Navigator.of(context).pop();
                  // TODO: Math Learning Plan Creation with AI-generated curriculum will be implemented
                  _showFeatureComingSoon('Math Learning Plan Creation');
                },
              ),
              ListTile(
                leading: const Icon(Icons.science),
                title: const Text('Science'),
                subtitle: const Text('Physics, Chemistry, Biology'),
                onTap: () {
                  Navigator.of(context).pop();
                  // TODO: Science Learning Plan Creation with lab activities and experiments will be implemented
                  _showFeatureComingSoon('Science Learning Plan Creation');
                },
              ),
              ListTile(
                leading: const Icon(Icons.language),
                title: const Text('Language Arts'),
                subtitle: const Text('Literature, Writing, Grammar'),
                onTap: () {
                  Navigator.of(context).pop();
                  // TODO: Language Arts Learning Plan Creation with reading and writing exercises will be implemented
                  _showFeatureComingSoon('Language Learning Plan Creation');
                },
              ),
              ListTile(
                leading: const Icon(Icons.add),
                title: const Text('Custom Subject'),
                subtitle: const Text('Create your own learning plan'),
                onTap: () {
                  Navigator.of(context).pop();
                  // TODO: Custom Learning Plan Creation with user-defined subjects and goals will be implemented
                  _showFeatureComingSoon('Custom Learning Plan Creation');
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showFeatureComingSoon(String featureName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$featureName coming soon!'),
        backgroundColor: AppTheme.primaryGreen,
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  Widget _buildProgressStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String subtitle,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const Spacer(),
              Icon(Icons.trending_up, color: color, size: 16),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 2),
          Text(
            subtitle,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivityList() {
    // TODO: Mock data will be replaced with real Firebase integration for user learning sessions
    final activities = [
      {
        'title': 'Completed Math Quiz',
        'subtitle': 'Algebra - Score: 85%',
        'time': '2 hours ago',
        'icon': Icons.quiz,
        'color': Colors.green,
      },
      {
        'title': 'Reviewed Flashcards',
        'subtitle': 'Geometry - 15 cards',
        'time': '4 hours ago',
        'icon': Icons.style,
        'color': Colors.blue,
      },
      {
        'title': 'Started Learning Plan',
        'subtitle': 'Calculus Fundamentals',
        'time': '1 day ago',
        'icon': Icons.school,
        'color': Colors.purple,
      },
    ];

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: activities.length,
      itemBuilder: (context, index) {
        final activity = activities[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: (activity['color'] as Color).withValues(
                alpha: 0.1,
              ),
              child: Icon(
                activity['icon'] as IconData,
                color: activity['color'] as Color,
                size: 20,
              ),
            ),
            title: Text(
              activity['title'] as String,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Text(activity['subtitle'] as String),
            trailing: Text(
              activity['time'] as String,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
            onTap: () {
              // TODO: Detailed activity view with performance analytics will be implemented
              _showFeatureComingSoon('Activity Details');
            },
          ),
        );
      },
    );
  }

  void _showProgressFilters() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.filter_list, color: AppTheme.primaryGreen),
            SizedBox(width: 8),
            Text('Filter Progress Data'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Time Period:'),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children:
                    ['Last 7 days', 'Last 30 days', 'Last 3 months', 'All time']
                        .map(
                          (period) => FilterChip(
                            label: Text(period),
                            selected: period == 'Last 7 days',
                            onSelected: (selected) {
                              // TODO: Time period filtering with data refresh will be implemented
                            },
                          ),
                        )
                        .toList(),
              ),
              const SizedBox(height: 16),
              const Text('Subject:'),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: ['All', 'Mathematics', 'Science', 'Language']
                    .map(
                      (subject) => FilterChip(
                        label: Text(subject),
                        selected: subject == 'All',
                        onSelected: (selected) {
                          // TODO: Subject-based filtering with multi-select support will be implemented
                        },
                      ),
                    )
                    .toList(),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Selected filters and progress data refresh with Firebase integration will be implemented
              _showFeatureComingSoon('Progress Filtering');
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _exportProgressData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.download, color: AppTheme.primaryGreen),
            SizedBox(width: 8),
            Text('Export Progress Data'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Choose export format:'),
            SizedBox(height: 16),
            ListTile(
              leading: Icon(Icons.table_chart),
              title: Text('CSV File'),
              subtitle: Text('Spreadsheet compatible format'),
            ),
            ListTile(
              leading: Icon(Icons.picture_as_pdf),
              title: Text('PDF Report'),
              subtitle: Text('Formatted progress report'),
            ),
            ListTile(
              leading: Icon(Icons.code),
              title: Text('JSON Data'),
              subtitle: Text('Raw data format'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: CSV/PDF export with user progress analytics and charts will be implemented
              _showFeatureComingSoon('Data Export');
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  // Missing methods from dashboard page merge

  /// Mock data method for stats - Note: Will be replaced with real data from BLoC state
  Map<String, dynamic> _getMockStats() {
    return {
      'totalCards': 42,
      'dueToday': 8,
      'mastered': 15,
      'completedQuizzes': 12,
      'averageScore': 85,
      'currentStreak': 7,
      'studyTime': '2h 30m',
      'conceptsLearned': 23,
      'accuracyRate': 87,
      'knowledgeGaps': 3,
    };
  }

  /// Navigate to analytics page
  void _navigateToAnalytics() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.analytics, color: Colors.blue),
            SizedBox(width: 8),
            Text('Learning Analytics'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '📊 Detailed analytics coming soon!',
            ), // TODO: Comprehensive analytics dashboard will be implemented
            SizedBox(height: 16),
            Text('Features will include:'),
            SizedBox(height: 8),
            Text('• Progress tracking over time'),
            Text('• Performance insights'),
            Text('• Learning pattern analysis'),
            Text('• Personalized recommendations'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  /// Generate learning plan with validation
  void _generateLearningPlan() {
    // Validate form data
    if (_selectedSubject == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please select a subject')));
      return;
    }

    if (_selectedLevel == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select your current level')),
      );
      return;
    }

    if (_learningGoals.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one learning goal')),
      );
      return;
    }

    // Collect form data and dispatch event
    context.read<AITutorBloc>().add(
      GenerateLearningPlanEvent(
        subject: _selectedSubject!,
        currentLevel: _selectedLevel!,
        learningGoals: List.from(_learningGoals),
        preferences: {
          'usageType': widget.usage?.type.toString() ?? 'UsageType.tutor',
          'timestamp': DateTime.now().toIso8601String(),
        },
      ),
    );
  }

  /// Show create flashcards dialog
  void _showCreateFlashcardsDialog() {
    showDialog(
      context: context,
      builder: (context) => CreateFlashcardsDialog(
        onCreateFlashcards: (topic, count) {
          _createFlashcardsWithAI(topic, count);
        },
      ),
    );
  }

  /// Creates flashcards using AI content service
  Future<void> _createFlashcardsWithAI(String topic, int count) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Generating flashcards with AI...'),
            ],
          ),
        ),
      );

      final aiContentService = AIContentService();
      final difficulty = _parseDifficultyLevel(_selectedDifficulty);

      final flashcards = await aiContentService.generateFlashcards(
        topic: topic,
        count: count,
        difficulty: difficulty,
        context: 'User-requested flashcards for learning',
      );

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Created $count flashcards for $topic!'),
            backgroundColor: Colors.green,
          ),
        );
      }

      // TODO: Navigate to flashcard review or save to user's collection
      developer.log(
        'Generated ${flashcards.length} flashcards for topic: $topic',
      );
    } catch (e) {
      // Close loading dialog if still open
      if (mounted) Navigator.of(context).pop();

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create flashcards: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }

      developer.log('Error creating flashcards: $e');
    }
  }

  /// Show create quiz dialog
  void _showCreateQuizDialog() {
    showDialog(
      context: context,
      builder: (context) => CreateQuizDialog(
        onCreateQuiz: (topic, questionCount, difficulty) {
          _createQuizWithAI(topic, questionCount, difficulty);
        },
      ),
    );
  }

  /// Creates a quiz using AI content service
  Future<void> _createQuizWithAI(
    String topic,
    int questionCount,
    String difficulty,
  ) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Generating adaptive quiz with AI...'),
            ],
          ),
        ),
      );

      final aiContentService = AIContentService();
      final difficultyLevel = _parseDifficultyLevel(difficulty);

      final quiz = await aiContentService.generateQuiz(
        topic: topic,
        concepts: [topic], // For now, use topic as the main concept
        difficulty: difficultyLevel,
        questionCount: questionCount,
      );

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Created $questionCount question quiz for $topic!'),
            backgroundColor: Colors.green,
          ),
        );
      }

      // TODO: Navigate to quiz taking interface or save to user's collection
      developer.log(
        'Generated quiz with ${quiz.questions.length} questions for topic: $topic',
      );
    } catch (e) {
      // Close loading dialog if still open
      if (mounted) Navigator.of(context).pop();

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create quiz: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }

      developer.log('Error creating quiz: $e');
    }
  }

  /// Build error widget for failed operations
  Widget _buildErrorWidget(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Oops! Something went wrong',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _generateLearningPlan(),
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }
}
