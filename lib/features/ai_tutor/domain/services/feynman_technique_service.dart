import 'dart:convert';
import 'dart:developer' as developer;
import 'package:langchain/langchain.dart';
import 'package:langchain_openai/langchain_openai.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../entities/learning_progress.dart';

/// Service for implementing the Feynman Technique in learning
class FeynmanTechniqueService {
  late final ChatOpenAI? _chatModel;

  FeynmanTechniqueService({String? apiKey}) {
    try {
      // Get API key from dotenv environment or use provided key
      final effectiveApiKey =
          apiKey ??
          dotenv.env['OPENAI_API_KEY'] ??
          const String.fromEnvironment('OPENAI_API_KEY', defaultValue: '');

      if (effectiveApiKey.isNotEmpty) {
        _chatModel = ChatOpenAI(
          apiKey: effectiveApiKey,
          defaultOptions: const ChatOpenAIOptions(
            model: 'gpt-3.5-turbo',
            temperature: 0.7,
            maxTokens: 1500,
          ),
        );
      } else {
        _chatModel = null;
        developer.log(
          'OpenAI API key not found. Feynman Technique will use fallback methods.',
          name: 'FeynmanTechniqueService',
        );
      }
    } catch (e) {
      _chatModel = null;
      developer.log(
        'Failed to initialize AI model: $e',
        name: 'FeynmanTechniqueService',
        error: e,
      );
    }
  }

  /// Adds Feynman Technique milestones to a learning plan
  Future<LearningPlan> addFeynmanMilestones(LearningPlan plan) async {
    final enhancedMilestones = <LearningMilestone>[];

    for (final milestone in plan.milestones) {
      // Add original milestone
      enhancedMilestones.add(milestone);

      // Add Feynman Technique milestone after each learning milestone
      final feynmanMilestone = _createFeynmanMilestone(milestone);
      enhancedMilestones.add(feynmanMilestone);
    }

    return plan.copyWith(
      milestones: enhancedMilestones,
      lastUpdated: DateTime.now(),
    );
  }

  /// Creates a Feynman session for a specific concept
  Future<FeynmanSession> createFeynmanSession({
    required String concept,
    required String userExplanation,
  }) async {
    // Step 1: Analyze user's explanation
    final analysis = await _analyzeExplanation(userExplanation);

    // Step 2: Identify knowledge gaps
    final gaps = await _identifyGaps(concept, userExplanation);

    // Step 3: Generate simplified explanations
    final simplifications = await _generateSimplifications(concept, gaps);

    // Step 4: Create analogies
    final analogies = await _generateAnalogies(concept);

    return FeynmanSession(
      concept: concept,
      userExplanation: userExplanation,
      analysis: analysis,
      knowledgeGaps: gaps,
      simplifications: simplifications,
      analogies: analogies,
      score: _calculateComprehensionScore(analysis, gaps),
    );
  }

  /// Evaluates an explanation using Feynman criteria
  Future<FeynmanEvaluation> evaluateExplanation({
    required String concept,
    required String explanation,
  }) async {
    final criteria = [
      _evaluateSimplicity(explanation),
      _evaluateClarity(explanation),
      _evaluateCompleteness(concept, explanation),
      _evaluateAccuracy(concept, explanation),
    ];

    final overallScore =
        criteria.map((c) => c.score).reduce((a, b) => a + b) / criteria.length;

    return FeynmanEvaluation(
      concept: concept,
      explanation: explanation,
      overallScore: overallScore,
      criteria: criteria,
      suggestions: _generateImprovementSuggestions(criteria),
    );
  }

  /// Generates teaching prompts for the Feynman Technique
  List<String> generateTeachingPrompts(String concept) {
    return [
      'Explain $concept as if you were teaching it to a 12-year-old.',
      'What is the most important thing to understand about $concept?',
      'How would you explain $concept using only simple words?',
      'What real-world example best illustrates $concept?',
      'If someone had never heard of $concept, how would you introduce it?',
      'What analogy would help someone understand $concept?',
      'What are the key steps or components of $concept?',
      'How does $concept connect to things people already know?',
    ];
  }

  /// Creates a Feynman milestone for a learning milestone
  LearningMilestone _createFeynmanMilestone(
    LearningMilestone originalMilestone,
  ) {
    final feynmanDate = originalMilestone.targetDate.add(
      const Duration(days: 2),
    );

    return LearningMilestone(
      id: '${originalMilestone.id}_feynman',
      title: 'Teach: ${originalMilestone.title}',
      description:
          'Explain the concepts from "${originalMilestone.title}" using the Feynman Technique. '
          'Practice teaching these concepts in simple terms as if explaining to a beginner.',
      concepts: originalMilestone.concepts,
      targetDate: feynmanDate,
      isCompleted: false,
      resources: [
        'Feynman Technique Guide',
        'Teaching Practice Exercises',
        'Concept Explanation Templates',
      ],
      metadata: {
        'type': 'feynman_technique',
        'originalMilestone': originalMilestone.id,
        'teachingPrompts': generateTeachingPrompts(originalMilestone.title),
      },
    );
  }

  /// Analyzes the quality of a user's explanation
  Future<ExplanationAnalysis> _analyzeExplanation(String explanation) async {
    // Implement AI-powered explanation analysis using LangChain
    if (_chatModel != null) {
      try {
        return await _analyzeExplanationWithAI(explanation);
      } catch (e) {
        developer.log(
          'AI explanation analysis failed, using fallback: $e',
          name: 'FeynmanTechniqueService',
          error: e,
        );
      }
    }

    // Fallback analysis when AI is not available
    return ExplanationAnalysis(
      wordCount: explanation.split(' ').length,
      readabilityScore: _calculateReadabilityScore(explanation),
      conceptCoverage: _estimateConceptCoverage(explanation),
      clarityScore: _estimateClarityScore(explanation),
      technicalTerms: _extractTechnicalTerms(explanation),
      suggestions: _generateBasicSuggestions(explanation),
    );
  }

  /// Uses AI to analyze explanation quality
  Future<ExplanationAnalysis> _analyzeExplanationWithAI(
    String explanation,
  ) async {
    final prompt =
        '''
Analyze the following explanation for quality using the Feynman Technique criteria:

Explanation: "$explanation"

Evaluate the explanation on these aspects and return a JSON response:
1. Concept Coverage (0.0-1.0): How well does it cover the key concepts?
2. Clarity Score (0.0-1.0): How clear and understandable is it?
3. Suggestions: List 3-5 specific suggestions for improvement

Format your response as JSON:
{
  "conceptCoverage": 0.8,
  "clarityScore": 0.7,
  "suggestions": [
    "Add more concrete examples",
    "Simplify technical language",
    "Break down complex ideas"
  ]
}
''';

    final response = await _chatModel!.invoke(PromptValue.string(prompt));
    final content = response.output.content;

    try {
      final jsonResponse = _extractJsonFromResponse(content);
      if (jsonResponse != null && jsonResponse is Map<String, dynamic>) {
        final conceptCoverage =
            (jsonResponse['conceptCoverage'] as num?)?.toDouble() ?? 0.7;
        final clarityScore =
            (jsonResponse['clarityScore'] as num?)?.toDouble() ?? 0.8;
        final suggestions =
            (jsonResponse['suggestions'] as List<dynamic>?)
                ?.map((e) => e.toString())
                .toList() ??
            [];

        return ExplanationAnalysis(
          wordCount: explanation.split(' ').length,
          readabilityScore: _calculateReadabilityScore(explanation),
          conceptCoverage: conceptCoverage,
          clarityScore: clarityScore,
          technicalTerms: _extractTechnicalTerms(explanation),
          suggestions: suggestions,
        );
      }
    } catch (parseError) {
      developer.log(
        'Failed to parse AI analysis response: $parseError',
        name: 'FeynmanTechniqueService',
        error: parseError,
      );
    }

    // Fallback if AI parsing fails
    return ExplanationAnalysis(
      wordCount: explanation.split(' ').length,
      readabilityScore: _calculateReadabilityScore(explanation),
      conceptCoverage: _estimateConceptCoverage(explanation),
      clarityScore: _estimateClarityScore(explanation),
      technicalTerms: _extractTechnicalTerms(explanation),
      suggestions: _generateBasicSuggestions(explanation),
    );
  }

  /// Identifies knowledge gaps in an explanation
  Future<List<String>> _identifyGaps(String concept, String explanation) async {
    // Use AI to identify missing key points using LangChain
    if (_chatModel != null) {
      try {
        return await _identifyGapsWithAI(concept, explanation);
      } catch (e) {
        developer.log(
          'AI gap identification failed, using fallback: $e',
          name: 'FeynmanTechniqueService',
          error: e,
        );
      }
    }

    // Fallback gap identification using basic heuristics
    return _identifyGapsBasic(concept, explanation);
  }

  /// Uses AI to identify knowledge gaps
  Future<List<String>> _identifyGapsWithAI(
    String concept,
    String explanation,
  ) async {
    final prompt =
        '''
Analyze the following explanation of "$concept" and identify key knowledge gaps:

Explanation: "$explanation"

Identify what important aspects are missing from this explanation. Consider:
1. Fundamental definitions
2. Key principles or mechanisms
3. Practical examples or applications
4. Common misconceptions that should be addressed
5. Prerequisites or foundational concepts

Return a JSON array of specific gaps:
["Missing fundamental definition", "No practical examples", "Key relationships not explained"]
''';

    final response = await _chatModel!.invoke(PromptValue.string(prompt));
    final content = response.output.content;

    try {
      final jsonResponse = _extractJsonFromResponse(content);
      if (jsonResponse is List) {
        return jsonResponse.map((e) => e.toString()).toList();
      }
    } catch (parseError) {
      developer.log(
        'Failed to parse AI gaps response: $parseError',
        name: 'FeynmanTechniqueService',
        error: parseError,
      );
    }

    // Fallback if AI parsing fails
    return _identifyGapsBasic(concept, explanation);
  }

  /// Basic gap identification using heuristics
  List<String> _identifyGapsBasic(String concept, String explanation) {
    final gaps = <String>[];
    final lowerExplanation = explanation.toLowerCase();

    // Check for missing definition
    if (!lowerExplanation.contains('is') &&
        !lowerExplanation.contains('means') &&
        !lowerExplanation.contains('refers to')) {
      gaps.add('Missing fundamental definition');
    }

    // Check for missing examples
    if (!lowerExplanation.contains('example') &&
        !lowerExplanation.contains('like') &&
        !lowerExplanation.contains('such as')) {
      gaps.add('No practical examples provided');
    }

    // Check for missing applications
    if (!lowerExplanation.contains('use') &&
        !lowerExplanation.contains('apply') &&
        !lowerExplanation.contains('purpose')) {
      gaps.add('Missing practical applications');
    }

    // Check for missing reasoning
    if (!lowerExplanation.contains('because') &&
        !lowerExplanation.contains('why') &&
        !lowerExplanation.contains('reason')) {
      gaps.add('Missing explanation of underlying reasons');
    }

    // Check if explanation is too brief
    if (explanation.split(' ').length < 20) {
      gaps.add('Explanation too brief - needs more detail');
    }

    return gaps;
  }

  /// Generates simplified explanations for knowledge gaps
  Future<List<String>> _generateSimplifications(
    String concept,
    List<String> gaps,
  ) async {
    // Generate simplified explanations for gaps using AI
    if (_chatModel != null && gaps.isNotEmpty) {
      try {
        return await _generateSimplificationsWithAI(concept, gaps);
      } catch (e) {
        developer.log(
          'AI simplification generation failed, using fallback: $e',
          name: 'FeynmanTechniqueService',
          error: e,
        );
      }
    }

    // Fallback simplifications
    return gaps.map((gap) => 'Simplified explanation for: $gap').toList();
  }

  /// Uses AI to generate simplified explanations
  Future<List<String>> _generateSimplificationsWithAI(
    String concept,
    List<String> gaps,
  ) async {
    final gapsText = gaps.join('\n- ');
    final prompt =
        '''
For the concept "$concept", provide simple, clear explanations to address these knowledge gaps:

- $gapsText

For each gap, provide a simplified explanation that:
1. Uses simple, everyday language
2. Includes concrete examples
3. Avoids technical jargon
4. Is suitable for a beginner

Return a JSON array of simplified explanations in the same order as the gaps:
["Simple explanation for gap 1", "Simple explanation for gap 2", ...]
''';

    final response = await _chatModel!.invoke(PromptValue.string(prompt));
    final content = response.output.content;

    try {
      final jsonResponse = _extractJsonFromResponse(content);
      if (jsonResponse is List) {
        return jsonResponse.map((e) => e.toString()).toList();
      }
    } catch (parseError) {
      developer.log(
        'Failed to parse AI simplifications response: $parseError',
        name: 'FeynmanTechniqueService',
        error: parseError,
      );
    }

    // Fallback if AI parsing fails
    return gaps.map((gap) => 'Simplified explanation for: $gap').toList();
  }

  /// Generates analogies for a concept
  Future<List<String>> _generateAnalogies(String concept) async {
    // Generate relevant analogies using AI
    if (_chatModel != null) {
      try {
        return await _generateAnalogiesWithAI(concept);
      } catch (e) {
        developer.log(
          'AI analogy generation failed, using fallback: $e',
          name: 'FeynmanTechniqueService',
          error: e,
        );
      }
    }

    // Fallback analogies
    return [
      'Think of $concept like...',
      'Imagine $concept as...',
      'You can compare $concept to...',
    ];
  }

  /// Uses AI to generate analogies
  Future<List<String>> _generateAnalogiesWithAI(String concept) async {
    final prompt =
        '''
Generate 3-5 helpful analogies to explain the concept "$concept".

Each analogy should:
1. Use familiar, everyday objects or situations
2. Highlight key aspects of the concept
3. Be easy to understand and remember
4. Help clarify how the concept works

Return a JSON array of analogies:
["$concept is like a library because...", "$concept is similar to cooking because...", ...]
''';

    final response = await _chatModel!.invoke(PromptValue.string(prompt));
    final content = response.output.content;

    try {
      final jsonResponse = _extractJsonFromResponse(content);
      if (jsonResponse is List) {
        return jsonResponse.map((e) => e.toString()).toList();
      }
    } catch (parseError) {
      developer.log(
        'Failed to parse AI analogies response: $parseError',
        name: 'FeynmanTechniqueService',
        error: parseError,
      );
    }

    // Fallback if AI parsing fails
    return [
      'Think of $concept like a familiar everyday object',
      'Imagine $concept as something you use regularly',
      'You can compare $concept to a simple process you know',
    ];
  }

  /// Calculates comprehension score based on analysis and gaps
  double _calculateComprehensionScore(
    ExplanationAnalysis analysis,
    List<String> gaps,
  ) {
    // Base score from analysis
    double score = (analysis.clarityScore + analysis.conceptCoverage) / 2;

    // Penalty for knowledge gaps
    final gapPenalty = gaps.length * 0.1;
    score = (score - gapPenalty).clamp(0.0, 1.0);

    return score;
  }

  /// Evaluates simplicity of explanation
  EvaluationCriterion _evaluateSimplicity(String explanation) {
    final words = explanation.split(' ');
    final avgWordLength =
        words.map((w) => w.length).reduce((a, b) => a + b) / words.length;

    // Simpler words = higher score
    final score = (10 - avgWordLength).clamp(0, 10) / 10;

    return EvaluationCriterion(
      name: 'Simplicity',
      score: score,
      feedback: score > 0.7
          ? 'Good use of simple language'
          : 'Try using simpler words and shorter sentences',
    );
  }

  /// Evaluates clarity of explanation
  EvaluationCriterion _evaluateClarity(String explanation) {
    final sentences = explanation.split(RegExp(r'[.!?]+'));
    final avgSentenceLength =
        sentences.map((s) => s.split(' ').length).reduce((a, b) => a + b) /
        sentences.length;

    // Shorter sentences = higher clarity
    final score = (25 - avgSentenceLength).clamp(0, 25) / 25;

    return EvaluationCriterion(
      name: 'Clarity',
      score: score,
      feedback: score > 0.7
          ? 'Clear and well-structured explanation'
          : 'Try breaking down complex sentences into simpler ones',
    );
  }

  /// Evaluates completeness of explanation
  EvaluationCriterion _evaluateCompleteness(
    String concept,
    String explanation,
  ) {
    // Mock completeness evaluation
    final hasDefinition =
        explanation.toLowerCase().contains('is') ||
        explanation.toLowerCase().contains('means');
    final hasExample =
        explanation.toLowerCase().contains('example') ||
        explanation.toLowerCase().contains('like');
    final hasApplication =
        explanation.toLowerCase().contains('use') ||
        explanation.toLowerCase().contains('apply');

    final completenessFactors = [hasDefinition, hasExample, hasApplication];
    final score =
        completenessFactors.where((f) => f).length / completenessFactors.length;

    return EvaluationCriterion(
      name: 'Completeness',
      score: score,
      feedback: score > 0.7
          ? 'Comprehensive explanation covering key aspects'
          : 'Consider adding definition, examples, and practical applications',
    );
  }

  /// Evaluates accuracy of explanation
  EvaluationCriterion _evaluateAccuracy(String concept, String explanation) {
    // Implement AI-powered accuracy checking using LangChain
    if (_chatModel != null) {
      try {
        // TODO: This would be async in a real implementation, but keeping sync for compatibility
        // In a production app, you'd want to make this async and update the calling code
        return _evaluateAccuracyBasic(concept, explanation);
      } catch (e) {
        developer.log(
          'AI accuracy evaluation failed, using fallback: $e',
          name: 'FeynmanTechniqueService',
          error: e,
        );
      }
    }

    // Fallback accuracy evaluation
    return _evaluateAccuracyBasic(concept, explanation);
  }

  /// Basic accuracy evaluation using heuristics
  EvaluationCriterion _evaluateAccuracyBasic(
    String concept,
    String explanation,
  ) {
    // Basic heuristic checks for accuracy
    double score = 0.7; // Base score
    String feedback = 'Explanation appears to be reasonable';

    final lowerExplanation = explanation.toLowerCase();
    final lowerConcept = concept.toLowerCase();

    // Check if explanation mentions the concept
    if (lowerExplanation.contains(lowerConcept)) {
      score += 0.1;
    }

    // Check for logical structure
    if (lowerExplanation.contains('because') ||
        lowerExplanation.contains('therefore') ||
        lowerExplanation.contains('as a result')) {
      score += 0.1;
      feedback = 'Good logical structure in explanation';
    }

    // Check for contradictory statements (basic)
    if (lowerExplanation.contains('not') && lowerExplanation.contains('is')) {
      // This is a very basic check - in reality you'd want more sophisticated analysis
      score -= 0.1;
    }

    // Check explanation length (too short might miss important details)
    final wordCount = explanation.split(' ').length;
    if (wordCount < 10) {
      score -= 0.2;
      feedback = 'Explanation may be too brief to be comprehensive';
    } else if (wordCount > 20) {
      score += 0.1;
      feedback = 'Comprehensive explanation with good detail';
    }

    return EvaluationCriterion(
      name: 'Accuracy',
      score: score.clamp(0.0, 1.0),
      feedback: feedback,
    );
  }

  /// Uses AI to evaluate explanation accuracy (for future async implementation)
  Future<EvaluationCriterion> _evaluateAccuracyWithAI(
    String concept,
    String explanation,
  ) async {
    final prompt =
        '''
Evaluate the factual accuracy of this explanation of "$concept":

Explanation: "$explanation"

Check for:
1. Factual correctness
2. Logical consistency
3. Common misconceptions
4. Missing critical information

Return a JSON response with:
{
  "score": 0.85,
  "feedback": "Detailed feedback about accuracy"
}

Score should be 0.0-1.0 where 1.0 is completely accurate.
''';

    final response = await _chatModel!.invoke(PromptValue.string(prompt));
    final content = response.output.content;

    try {
      final jsonResponse = _extractJsonFromResponse(content);
      if (jsonResponse != null && jsonResponse is Map<String, dynamic>) {
        final score = (jsonResponse['score'] as num?)?.toDouble() ?? 0.8;
        final feedback =
            jsonResponse['feedback'] as String? ?? 'AI evaluation completed';

        return EvaluationCriterion(
          name: 'Accuracy',
          score: score.clamp(0.0, 1.0),
          feedback: feedback,
        );
      }
    } catch (parseError) {
      developer.log(
        'Failed to parse AI accuracy response: $parseError',
        name: 'FeynmanTechniqueService',
        error: parseError,
      );
    }

    // Fallback if AI parsing fails
    return _evaluateAccuracyBasic(concept, explanation);
  }

  /// Public method to evaluate explanation accuracy using AI
  /// This method provides access to AI-powered accuracy evaluation
  Future<EvaluationCriterion> evaluateAccuracyWithAI(
    String concept,
    String explanation,
  ) async {
    return await _evaluateAccuracyWithAI(concept, explanation);
  }

  /// Generates improvement suggestions based on evaluation criteria
  List<String> _generateImprovementSuggestions(
    List<EvaluationCriterion> criteria,
  ) {
    final suggestions = <String>[];

    for (final criterion in criteria) {
      if (criterion.score < 0.7) {
        suggestions.add(criterion.feedback);
      }
    }

    if (suggestions.isEmpty) {
      suggestions.add(
        'Great explanation! Try teaching it to someone else to reinforce your understanding.',
      );
    }

    return suggestions;
  }

  /// Calculates readability score (simplified)
  double _calculateReadabilityScore(String text) {
    final words = text.split(' ').length;
    final sentences = text.split(RegExp(r'[.!?]+')).length;

    if (sentences == 0) return 0.0;

    final avgWordsPerSentence = words / sentences;

    // Simpler = higher score
    return (20 - avgWordsPerSentence).clamp(0, 20) / 20;
  }

  /// Extracts technical terms from explanation
  List<String> _extractTechnicalTerms(String explanation) {
    // Simple heuristic: words longer than 8 characters
    final words = explanation.split(' ');
    return words.where((word) => word.length > 8).toList();
  }

  // Helper methods for fallback analysis

  /// Estimates concept coverage using basic heuristics
  double _estimateConceptCoverage(String explanation) {
    final words = explanation.split(' ');
    final hasDefinition =
        explanation.toLowerCase().contains('is') ||
        explanation.toLowerCase().contains('means') ||
        explanation.toLowerCase().contains('refers to');
    final hasExample =
        explanation.toLowerCase().contains('example') ||
        explanation.toLowerCase().contains('like') ||
        explanation.toLowerCase().contains('such as');
    final hasApplication =
        explanation.toLowerCase().contains('use') ||
        explanation.toLowerCase().contains('apply') ||
        explanation.toLowerCase().contains('purpose');

    double score = 0.3; // Base score
    if (hasDefinition) score += 0.3;
    if (hasExample) score += 0.2;
    if (hasApplication) score += 0.2;

    // Bonus for length (more comprehensive)
    if (words.length > 50) score += 0.1;
    if (words.length > 100) score += 0.1;

    return score.clamp(0.0, 1.0);
  }

  /// Estimates clarity score using basic heuristics
  double _estimateClarityScore(String explanation) {
    final sentences = explanation.split(RegExp(r'[.!?]+'));
    if (sentences.isEmpty) return 0.0;

    final avgSentenceLength = explanation.split(' ').length / sentences.length;
    final hasTransitions =
        explanation.toLowerCase().contains('first') ||
        explanation.toLowerCase().contains('then') ||
        explanation.toLowerCase().contains('finally') ||
        explanation.toLowerCase().contains('because');

    double score = 0.5; // Base score

    // Shorter sentences are clearer
    if (avgSentenceLength < 15) {
      score += 0.2;
    } else if (avgSentenceLength > 25) {
      score -= 0.2;
    }

    // Transitions improve clarity
    if (hasTransitions) score += 0.2;

    // Avoid too many technical terms
    final techTerms = _extractTechnicalTerms(explanation);
    final techRatio = techTerms.length / explanation.split(' ').length;
    if (techRatio > 0.2) score -= 0.2;

    return score.clamp(0.0, 1.0);
  }

  /// Generates basic suggestions for improvement
  List<String> _generateBasicSuggestions(String explanation) {
    final suggestions = <String>[];
    final words = explanation.split(' ');
    final sentences = explanation.split(RegExp(r'[.!?]+'));

    if (words.length < 30) {
      suggestions.add('Try to provide more detail in your explanation');
    }

    if (sentences.isNotEmpty && words.length / sentences.length > 20) {
      suggestions.add('Break down long sentences into shorter, clearer ones');
    }

    if (!explanation.toLowerCase().contains('example') &&
        !explanation.toLowerCase().contains('like')) {
      suggestions.add('Add concrete examples to illustrate your points');
    }

    final techTerms = _extractTechnicalTerms(explanation);
    if (techTerms.length > 3) {
      suggestions.add(
        'Try to use simpler language and explain technical terms',
      );
    }

    if (!explanation.toLowerCase().contains('because') &&
        !explanation.toLowerCase().contains('why')) {
      suggestions.add('Explain the reasoning behind key concepts');
    }

    if (suggestions.isEmpty) {
      suggestions.add(
        'Good explanation! Try teaching it to someone else to test your understanding',
      );
    }

    return suggestions;
  }

  /// Extracts JSON from AI response content
  dynamic _extractJsonFromResponse(String content) {
    try {
      // Try to find JSON in the response (object or array)
      final jsonStart = content.indexOf(RegExp(r'[{\[]'));
      final jsonEnd = content.lastIndexOf(RegExp(r'[}\]]'));

      if (jsonStart != -1 && jsonEnd != -1 && jsonEnd > jsonStart) {
        final jsonString = content.substring(jsonStart, jsonEnd + 1);
        return json.decode(jsonString);
      }

      // If no JSON found, try to parse the entire content
      return json.decode(content);
    } catch (e) {
      developer.log(
        'Failed to extract JSON from response: $e',
        name: 'FeynmanTechniqueService',
        error: e,
      );
      return null;
    }
  }
}

/// Represents a Feynman Technique learning session
class FeynmanSession {
  final String concept;
  final String userExplanation;
  final ExplanationAnalysis analysis;
  final List<String> knowledgeGaps;
  final List<String> simplifications;
  final List<String> analogies;
  final double score;

  const FeynmanSession({
    required this.concept,
    required this.userExplanation,
    required this.analysis,
    required this.knowledgeGaps,
    required this.simplifications,
    required this.analogies,
    required this.score,
  });

  @override
  String toString() {
    return 'FeynmanSession(concept: $concept, score: ${(score * 100).toStringAsFixed(1)}%)';
  }
}

/// Represents analysis of an explanation
class ExplanationAnalysis {
  final int wordCount;
  final double readabilityScore;
  final double conceptCoverage;
  final double clarityScore;
  final List<String> technicalTerms;
  final List<String> suggestions;

  const ExplanationAnalysis({
    required this.wordCount,
    required this.readabilityScore,
    required this.conceptCoverage,
    required this.clarityScore,
    required this.technicalTerms,
    required this.suggestions,
  });
}

/// Represents evaluation of an explanation using Feynman criteria
class FeynmanEvaluation {
  final String concept;
  final String explanation;
  final double overallScore;
  final List<EvaluationCriterion> criteria;
  final List<String> suggestions;

  const FeynmanEvaluation({
    required this.concept,
    required this.explanation,
    required this.overallScore,
    required this.criteria,
    required this.suggestions,
  });

  @override
  String toString() {
    return 'FeynmanEvaluation(concept: $concept, score: ${(overallScore * 100).toStringAsFixed(1)}%)';
  }
}

/// Represents a single evaluation criterion
class EvaluationCriterion {
  final String name;
  final double score;
  final String feedback;

  const EvaluationCriterion({
    required this.name,
    required this.score,
    required this.feedback,
  });

  @override
  String toString() {
    return 'EvaluationCriterion(name: $name, score: ${(score * 100).toStringAsFixed(1)}%)';
  }
}
