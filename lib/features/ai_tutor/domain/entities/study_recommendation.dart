import 'package:equatable/equatable.dart';

/// Represents a study recommendation for personalized learning
class StudyRecommendation extends Equatable {
  final String id;
  final String title;
  final String description;
  final RecommendationType type;
  final String subject;
  final String? topic;
  final int priority; // 1-5, 5 being highest
  final Duration estimatedTime;
  final Map<String, dynamic> metadata;

  const StudyRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.subject,
    this.topic,
    required this.priority,
    required this.estimatedTime,
    required this.metadata,
  });

  /// Creates a copy of this recommendation with the given fields replaced
  StudyRecommendation copyWith({
    String? id,
    String? title,
    String? description,
    RecommendationType? type,
    String? subject,
    String? topic,
    int? priority,
    Duration? estimatedTime,
    Map<String, dynamic>? metadata,
  }) {
    return StudyRecommendation(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      subject: subject ?? this.subject,
      topic: topic ?? this.topic,
      priority: priority ?? this.priority,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        type,
        subject,
        topic,
        priority,
        estimatedTime,
        metadata,
      ];

  @override
  String toString() {
    return 'StudyRecommendation(title: $title, type: $type, priority: $priority)';
  }
}

/// Enum representing different types of study recommendations
enum RecommendationType {
  reviewFlashcards,
  takeQuiz,
  studyConcept,
  practiceProblems,
  readMaterial,
  watchVideo,
  reviewWeakConcepts,
  practiceFlashcards,
}

/// Extension for recommendation type display and functionality
extension RecommendationTypeExtension on RecommendationType {
  /// Gets the display name for the recommendation type
  String get displayName {
    switch (this) {
      case RecommendationType.reviewFlashcards:
        return 'Review Flashcards';
      case RecommendationType.takeQuiz:
        return 'Take Quiz';
      case RecommendationType.studyConcept:
        return 'Study Concept';
      case RecommendationType.practiceProblems:
        return 'Practice Problems';
      case RecommendationType.readMaterial:
        return 'Read Material';
      case RecommendationType.watchVideo:
        return 'Watch Video';
      case RecommendationType.reviewWeakConcepts:
        return 'Review Weak Concepts';
      case RecommendationType.practiceFlashcards:
        return 'Practice Flashcards';
    }
  }

  /// Gets the icon name for the recommendation type
  String get iconName {
    switch (this) {
      case RecommendationType.reviewFlashcards:
        return 'style';
      case RecommendationType.takeQuiz:
        return 'quiz';
      case RecommendationType.studyConcept:
        return 'school';
      case RecommendationType.practiceProblems:
        return 'calculate';
      case RecommendationType.readMaterial:
        return 'menu_book';
      case RecommendationType.watchVideo:
        return 'play_circle';
      case RecommendationType.reviewWeakConcepts:
        return 'trending_down';
      case RecommendationType.practiceFlashcards:
        return 'style';
    }
  }

  /// Gets the color associated with the recommendation type
  String get colorName {
    switch (this) {
      case RecommendationType.reviewFlashcards:
        return 'blue';
      case RecommendationType.takeQuiz:
        return 'green';
      case RecommendationType.studyConcept:
        return 'purple';
      case RecommendationType.practiceProblems:
        return 'orange';
      case RecommendationType.readMaterial:
        return 'teal';
      case RecommendationType.watchVideo:
        return 'red';
      case RecommendationType.reviewWeakConcepts:
        return 'amber';
      case RecommendationType.practiceFlashcards:
        return 'indigo';
    }
  }
}
