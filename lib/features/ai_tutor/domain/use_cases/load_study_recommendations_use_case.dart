import 'package:dartz/dartz.dart';
import '../entities/study_recommendation.dart';
import '../repositories/ai_tutor_repository.dart';
import '../../../../core/error/failures.dart';
import '../../../../util/util_api_usage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:uuid/uuid.dart';

/// Use case for loading personalized study recommendations
class LoadStudyRecommendationsUseCase {
  final AITutorRepository _repository;

  LoadStudyRecommendationsUseCase(this._repository);

  /// Loads study recommendations based on user's learning history and preferences
  Future<Either<Failure, List<StudyRecommendation>>> call(
    LoadStudyRecommendationsParams params,
  ) async {
    try {
      // Validate parameters
      final validationResult = _validateParams(params);
      if (validationResult.isLeft()) {
        return validationResult.fold(
          (failure) => Left(failure),
          (_) => const Right([]), // This won't be reached
        );
      }

      // Load study recommendations using repository
      final result = await _repository.generateStudyRecommendations(
        userId: params.userId,
        subject: params.subject,
      );

      return result.fold((failure) => Left(failure), (recommendations) async {
        // Track API usage for study recommendations
        await _trackApiUsage(params, recommendations);
        return Right(recommendations);
      });
    } catch (e) {
      return Left(
        ServerFailure('Failed to load study recommendations: ${e.toString()}'),
      );
    }
  }

  /// Validates the parameters for loading study recommendations
  Either<Failure, void> _validateParams(LoadStudyRecommendationsParams params) {
    if (params.userId.trim().isEmpty) {
      return const Left(ValidationFailure('User ID cannot be empty'));
    }

    if (params.subject.trim().isEmpty) {
      return const Left(ValidationFailure('Subject cannot be empty'));
    }

    if (params.subject.length < 2) {
      return const Left(
        ValidationFailure('Subject must be at least 2 characters long'),
      );
    }

    return const Right(null);
  }

  /// Tracks API usage for study recommendations
  Future<void> _trackApiUsage(
    LoadStudyRecommendationsParams params,
    List<StudyRecommendation> recommendations,
  ) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      final responseId = const Uuid().v4();
      final questionContent =
          'Load study recommendations for user: ${params.userId} '
          'and subject: ${params.subject}';

      final responseContent =
          'Generated ${recommendations.length} study recommendations';

      await Util_API_Usage.saveApiUsage(
        responseId: responseId,
        apiUrl: 'ai_tutor/study_recommendations',
        usageType: 'UsageType.tutor',
        questionContent: questionContent,
        responseMessage: responseContent,
        currentUserId: user.uid,
        conversationId: user.uid,
        question: questionContent,
        model: 'ai_tutor_study_recommendations',
      );
    } catch (e) {
      // Log error but don't fail the operation
      print('Failed to track API usage for study recommendations: $e');
    }
  }
}

/// Parameters for loading study recommendations
class LoadStudyRecommendationsParams {
  final String userId;
  final String subject;

  const LoadStudyRecommendationsParams({
    required this.userId,
    required this.subject,
  });

  @override
  String toString() {
    return 'LoadStudyRecommendationsParams(userId: $userId, subject: $subject)';
  }
}
